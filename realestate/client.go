package realestate

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"time"
)

// Client represents the RealEstateAPI client
type Client struct {
	APIKey     string
	BaseURL    string
	HTTPClient *http.Client
}

// NewClient creates a new RealEstateAPI client
// Note: This client bypasses residential proxies as RealEstateAPI.com is a legitimate API service
func NewClient() *Client {
	apiKey := os.Getenv("REAL_ESTATE_API_KEY")
	if apiKey == "" {
		apiKey = "AYOKASYSTEMS-7ba4-759c-8c51-b5ec75ab2914" // Default fallback
	}

	// Create HTTP client with direct connection (no proxy)
	// RealEstateAPI.com is a legitimate API service that doesn't require residential proxies
	return &Client{
		APIKey:  apiKey,
		BaseURL: "https://api.realestateapi.com",
		HTTPClient: &http.Client{
			Timeout: 30 * time.Second,
			// No proxy configuration - direct connection to RealEstateAPI.com
		},
	}
}

// MakeRequest makes a generic HTTP request to the RealEstateAPI
func (c *Client) MakeRequest(endpoint string, payload interface{}) (*http.Response, error) {
	// Marshal payload to JSON
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request payload: %w", err)
	}

	// Create request
	url := c.BaseURL + endpoint
	log.Printf("Making RealEstateAPI request to: %s", url)
	log.Printf("Request payload: %s", string(jsonData))

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-API-KEY", c.APIKey)
	req.Header.Set("x-user-id", "PropBoltBrainAPI")

	log.Printf("Using API key: %s", c.APIKey[:10]+"...")

	// Make request
	log.Printf("Sending request to RealEstateAPI...")
	resp, err := c.HTTPClient.Do(req)
	if err != nil {
		log.Printf("Request failed: %v", err)
		return nil, fmt.Errorf("failed to make request: %w", err)
	}

	log.Printf("Received response with status: %d", resp.StatusCode)
	return resp, nil
}

// ParseResponse parses the HTTP response and returns the data
func (c *Client) ParseResponse(resp *http.Response, result interface{}) error {
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	if err := json.Unmarshal(body, result); err != nil {
		return fmt.Errorf("failed to unmarshal response: %w", err)
	}

	return nil
}

// APIResponse represents a generic API response wrapper
type APIResponse struct {
	Success    bool        `json:"success"`
	Data       interface{} `json:"data,omitempty"`
	StatusCode int         `json:"statusCode,omitempty"`
	Message    string      `json:"message,omitempty"`
	Error      string      `json:"error,omitempty"`
}

// ErrorResponse represents an error response from the API
type ErrorResponse struct {
	StatusCode    int    `json:"statusCode"`
	StatusMessage string `json:"statusMessage"`
	Message       string `json:"message"`
	Timestamp     string `json:"timestamp"`
}
