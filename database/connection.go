package database

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"time"

	_ "github.com/lib/pq"
)

var DB *sql.DB

// Property represents a vacant land property in Daytona Beach
type Property struct {
	ID                   int     `json:"id"`
	Address              string  `json:"address"`
	Price                int     `json:"price"`
	Size                 string  `json:"size"`
	Zoning               string  `json:"zoning"`
	Latitude             float64 `json:"lat"`
	Longitude            float64 `json:"lng"`
	Description          string  `json:"description"`
	Habitability         string  `json:"habitability"`
	Proximity            string  `json:"proximity"`
	ChainLeasePotential  string  `json:"chainLeasePotential"`
	DaysOnMarket         int     `json:"daysOnMarket"`
	PricePerSqFt         float64 `json:"pricePerSqFt"`
	CreatedAt            time.Time `json:"createdAt"`
	UpdatedAt            time.Time `json:"updatedAt"`
}

// User represents a user in the system
type User struct {
	ID           int       `json:"id"`
	Username     string    `json:"username"`
	Email        string    `json:"email"`
	PasswordHash string    `json:"-"` // Don't expose password hash in JSON
	Role         string    `json:"role"`
	AccountType  *string   `json:"accountType"` // "land", "data", or NULL
	CreatedAt    time.Time `json:"createdAt"`
	UpdatedAt    time.Time `json:"updatedAt"`
}

// WatchlistItem represents a property in a user's watchlist
type WatchlistItem struct {
	ID         int       `json:"id"`
	UserID     int       `json:"userId"`
	PropertyID int       `json:"propertyId"`
	Notes      string    `json:"notes"`
	CreatedAt  time.Time `json:"createdAt"`
}

// SearchQuery represents a saved search
type SearchQuery struct {
	ID        int       `json:"id"`
	UserID    int       `json:"userId"`
	Name      string    `json:"name"`
	Query     string    `json:"query"`
	Filters   string    `json:"filters"` // JSON string
	CreatedAt time.Time `json:"createdAt"`
}

// InitDB initializes the database connection
func InitDB() error {
	// Get database URL from environment variable
	databaseURL := os.Getenv("DATABASE_URL")
	if databaseURL == "" {
		// Check if we're in development (proxy connection) or production (socket connection)
		if os.Getenv("NODE_ENV") == "production" {
			// Production: Use Cloud SQL socket connection
			databaseURL = "**************************************************************************************************************************"
		} else {
			// Development: Use proxy connection on localhost:5432 with SSL disabled
			databaseURL = "**************************************************************************************"
		}
	}

	var err error
	DB, err = sql.Open("postgres", databaseURL)
	if err != nil {
		return fmt.Errorf("failed to open database: %v", err)
	}

	// Test the connection
	if err = DB.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %v", err)
	}

	// Set connection pool settings
	DB.SetMaxOpenConns(25)
	DB.SetMaxIdleConns(5)
	DB.SetConnMaxLifetime(5 * time.Minute)

	log.Println("Successfully connected to Google Cloud SQL PostgreSQL database")

	// Create tables if they don't exist
	if err = createTables(); err != nil {
		return fmt.Errorf("failed to create tables: %v", err)
	}

	log.Println("Production database initialized - no sample data inserted")

	return nil
}

// createTables creates the necessary tables
func createTables() error {
	queries := []string{
		`CREATE TABLE IF NOT EXISTS users (
			id SERIAL PRIMARY KEY,
			username VARCHAR(255) UNIQUE NOT NULL,
			email VARCHAR(255) UNIQUE NOT NULL,
			password_hash VARCHAR(255) NOT NULL,
			role VARCHAR(50) DEFAULT 'viewer',
			account_type VARCHAR(20) DEFAULT NULL,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)`,
		`CREATE TABLE IF NOT EXISTS properties (
			id SERIAL PRIMARY KEY,
			address VARCHAR(500) NOT NULL UNIQUE,
			price INTEGER NOT NULL,
			size VARCHAR(100),
			zoning VARCHAR(100),
			latitude DECIMAL(10, 8),
			longitude DECIMAL(11, 8),
			description TEXT,
			habitability VARCHAR(100),
			proximity VARCHAR(200),
			chain_lease_potential VARCHAR(50),
			days_on_market INTEGER DEFAULT 0,
			price_per_sqft DECIMAL(10, 2),
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)`,
		`CREATE TABLE IF NOT EXISTS watchlist_items (
			id SERIAL PRIMARY KEY,
			user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
			property_id INTEGER REFERENCES properties(id) ON DELETE CASCADE,
			notes TEXT,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			UNIQUE(user_id, property_id)
		)`,
		`CREATE TABLE IF NOT EXISTS search_queries (
			id SERIAL PRIMARY KEY,
			user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
			name VARCHAR(255) NOT NULL,
			query TEXT,
			filters JSONB,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)`,
		`CREATE INDEX IF NOT EXISTS idx_properties_zoning ON properties(zoning)`,
		`CREATE INDEX IF NOT EXISTS idx_properties_price ON properties(price)`,
		`CREATE INDEX IF NOT EXISTS idx_properties_location ON properties(latitude, longitude)`,
		`CREATE INDEX IF NOT EXISTS idx_watchlist_user ON watchlist_items(user_id)`,
	}

	for _, query := range queries {
		if _, err := DB.Exec(query); err != nil {
			return fmt.Errorf("failed to execute query %s: %v", query, err)
		}
	}

	// Migration: Add account_type column if it doesn't exist
	migrationQueries := []string{
		`ALTER TABLE users ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255)`,
		`ALTER TABLE users ADD COLUMN IF NOT EXISTS account_type VARCHAR(20) DEFAULT NULL`,
	}

	for _, query := range migrationQueries {
		if _, err := DB.Exec(query); err != nil {
			log.Printf("Migration warning (may be expected): %v", err)
		}
	}

	log.Println("Database tables created successfully with account_type support")
	return nil
}

// Production database functions - no sample data insertion

// GetProperties retrieves vacant land properties with optional filters
// Note: All properties in database are vacant land only (filtered at API level)
func GetProperties(filters map[string]interface{}) ([]Property, error) {
	query := `SELECT id, address, price, size, zoning, latitude, longitude, description,
			  habitability, proximity, chain_lease_potential, days_on_market, price_per_sqft,
			  created_at, updated_at FROM properties WHERE 1=1`

	args := []interface{}{}
	argIndex := 1

	// Add filters for vacant land properties
	if zoning, ok := filters["zoning"].(string); ok && zoning != "" {
		query += fmt.Sprintf(" AND zoning = $%d", argIndex)
		args = append(args, zoning)
		argIndex++
	}

	if minPrice, ok := filters["minPrice"].(int); ok && minPrice > 0 {
		query += fmt.Sprintf(" AND price >= $%d", argIndex)
		args = append(args, minPrice)
		argIndex++
	}

	if maxPrice, ok := filters["maxPrice"].(int); ok && maxPrice > 0 {
		query += fmt.Sprintf(" AND price <= $%d", argIndex)
		args = append(args, maxPrice)
		argIndex++
	}

	if chainPotential, ok := filters["chainPotential"].(string); ok && chainPotential != "" {
		query += fmt.Sprintf(" AND chain_lease_potential = $%d", argIndex)
		args = append(args, chainPotential)
		argIndex++
	}

	// Location filter (search in address field)
	if location, ok := filters["location"].(string); ok && location != "" {
		query += fmt.Sprintf(" AND address ILIKE $%d", argIndex)
		args = append(args, "%"+location+"%")
		argIndex++
	}

	query += " ORDER BY created_at DESC LIMIT 50"

	rows, err := DB.Query(query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var properties []Property
	for rows.Next() {
		var p Property
		err := rows.Scan(&p.ID, &p.Address, &p.Price, &p.Size, &p.Zoning, &p.Latitude, &p.Longitude,
			&p.Description, &p.Habitability, &p.Proximity, &p.ChainLeasePotential, &p.DaysOnMarket,
			&p.PricePerSqFt, &p.CreatedAt, &p.UpdatedAt)
		if err != nil {
			return nil, err
		}
		properties = append(properties, p)
	}

	return properties, nil
}

// GetDashboardStats retrieves dashboard statistics
func GetDashboardStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// Total properties
	var totalProperties int
	err := DB.QueryRow("SELECT COUNT(*) FROM properties").Scan(&totalProperties)
	if err != nil {
		return nil, err
	}
	stats["totalProperties"] = totalProperties

	// Average price
	var avgPrice float64
	err = DB.QueryRow("SELECT AVG(price) FROM properties").Scan(&avgPrice)
	if err != nil {
		return nil, err
	}
	stats["averagePrice"] = int(avgPrice)

	// New listings (last 24 hours)
	var newListings int
	err = DB.QueryRow("SELECT COUNT(*) FROM properties WHERE created_at > NOW() - INTERVAL '24 hours'").Scan(&newListings)
	if err != nil {
		return nil, err
	}
	stats["newListings"] = newListings

	// Sold this month (mock data for now)
	stats["soldThisMonth"] = 8

	// Zoning breakdown
	zoningQuery := `SELECT zoning, COUNT(*) FROM properties GROUP BY zoning`
	rows, err := DB.Query(zoningQuery)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	zoningBreakdown := make(map[string]int)
	for rows.Next() {
		var zoning string
		var count int
		if err := rows.Scan(&zoning, &count); err != nil {
			return nil, err
		}
		zoningBreakdown[zoning] = count
	}
	stats["zoningBreakdown"] = zoningBreakdown

	// Price ranges
	priceRanges := make(map[string]int)
	priceQueries := map[string]string{
		"Under $100k":    "SELECT COUNT(*) FROM properties WHERE price < 100000",
		"$100k-$200k":    "SELECT COUNT(*) FROM properties WHERE price >= 100000 AND price < 200000",
		"$200k-$500k":    "SELECT COUNT(*) FROM properties WHERE price >= 200000 AND price < 500000",
		"Over $500k":     "SELECT COUNT(*) FROM properties WHERE price >= 500000",
	}

	for label, query := range priceQueries {
		var count int
		if err := DB.QueryRow(query).Scan(&count); err != nil {
			return nil, err
		}
		priceRanges[label] = count
	}
	stats["priceRanges"] = priceRanges

	return stats, nil
}

// UpsertProperty inserts or updates a property in the database
func UpsertProperty(property Property) error {
	query := `
		INSERT INTO properties (address, price, size, zoning, latitude, longitude, description,
		                       habitability, proximity, chain_lease_potential, days_on_market, price_per_sqft)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
		ON CONFLICT (address)
		DO UPDATE SET
			price = EXCLUDED.price,
			size = EXCLUDED.size,
			zoning = EXCLUDED.zoning,
			latitude = EXCLUDED.latitude,
			longitude = EXCLUDED.longitude,
			description = EXCLUDED.description,
			habitability = EXCLUDED.habitability,
			proximity = EXCLUDED.proximity,
			chain_lease_potential = EXCLUDED.chain_lease_potential,
			days_on_market = EXCLUDED.days_on_market,
			price_per_sqft = EXCLUDED.price_per_sqft,
			updated_at = CURRENT_TIMESTAMP`

	_, err := DB.Exec(query, property.Address, property.Price, property.Size, property.Zoning,
		property.Latitude, property.Longitude, property.Description, property.Habitability,
		property.Proximity, property.ChainLeasePotential, property.DaysOnMarket, property.PricePerSqFt)

	return err
}

// GetUserByEmail retrieves a user by email for authentication
func GetUserByEmail(email string) (*User, error) {
	var user User
	query := `SELECT id, username, email, password_hash, role, account_type, created_at, updated_at
			  FROM users WHERE email = $1`

	err := DB.QueryRow(query, email).Scan(
		&user.ID, &user.Username, &user.Email, &user.PasswordHash,
		&user.Role, &user.AccountType, &user.CreatedAt, &user.UpdatedAt,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("user not found")
		}
		return nil, err
	}

	return &user, nil
}

// Production database functions - user authentication integrated

// CloseDB closes the database connection
func CloseDB() error {
	if DB != nil {
		return DB.Close()
	}
	return nil
}
