'use client';

import React, { useState } from 'react';
import { GoogleMapsComponent } from './GoogleMapsComponent';
import { Map, ToggleLeft, ToggleRight, Zap, DollarSign, Database } from 'lucide-react';

interface MapComparisonProps {
  properties?: Array<{
    id: string;
    lat: number;
    lng: number;
    address: string;
    price?: number;
    acres?: number;
    zoning?: string;
  }>;
  onPropertyClick?: (property: any) => void;
}

export function MapComparison({ properties = [], onPropertyClick }: MapComparisonProps) {
  const [activeMap, setActiveMap] = useState<'mapbox' | 'google'>('google');
  const [showComparison, setShowComparison] = useState(false);

  // Sample properties for Daytona Beach area if none provided
  const sampleProperties = properties.length > 0 ? properties : [
    {
      id: '1',
      lat: 29.2108,
      lng: -81.0228,
      address: '123 Main St, Daytona Beach, FL',
      price: 150000,
      acres: 2.5,
      zoning: 'Residential'
    },
    {
      id: '2',
      lat: 29.1950,
      lng: -81.0389,
      address: '456 Ocean Ave, Daytona Beach, FL',
      price: 300000,
      acres: 1.8,
      zoning: 'Commercial'
    }
  ];

  const mapboxFeatures = [
    { icon: Database, text: 'Zillow property dataset integration', status: 'current' },
    { icon: DollarSign, text: 'Cost: $5-15/1000 requests', status: 'current' },
    { icon: Zap, text: 'Performance: Good for property visualization', status: 'current' }
  ];

  const googleMapsFeatures = [
    { icon: Database, text: 'Google property data + custom datasets', status: 'new' },
    { icon: DollarSign, text: 'Cost: $2-7/1000 requests (lower cost)', status: 'better' },
    { icon: Zap, text: 'Performance: Superior geocoding & places API', status: 'better' }
  ];

  return (
    <div className="space-y-6">
      {/* Map Service Toggle */}
      <div className="flex items-center justify-between bg-white p-4 rounded-lg shadow">
        <div className="flex items-center space-x-3">
          <Map className="h-5 w-5 text-blue-600" />
          <h3 className="text-lg font-semibold text-gray-900">Map Service</h3>
        </div>
        
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setShowComparison(!showComparison)}
            className="text-sm text-blue-600 hover:text-blue-700 font-medium"
          >
            {showComparison ? 'Hide' : 'Show'} Comparison
          </button>
          
          <div className="flex items-center space-x-3">
            <span className={`text-sm font-medium ${activeMap === 'mapbox' ? 'text-blue-600' : 'text-gray-500'}`}>
              Mapbox
            </span>
            <button
              onClick={() => setActiveMap(activeMap === 'mapbox' ? 'google' : 'mapbox')}
              className="relative"
            >
              {activeMap === 'google' ? (
                <ToggleRight className="h-6 w-6 text-green-600" />
              ) : (
                <ToggleLeft className="h-6 w-6 text-gray-400" />
              )}
            </button>
            <span className={`text-sm font-medium ${activeMap === 'google' ? 'text-green-600' : 'text-gray-500'}`}>
              Google Maps
            </span>
          </div>
        </div>
      </div>

      {/* Comparison Table */}
      {showComparison && (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h4 className="text-lg font-semibold text-gray-900">Service Comparison</h4>
            <p className="text-sm text-gray-600">Evaluate map services for PropBolt land search</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-6">
            {/* Mapbox Column */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                <h5 className="font-semibold text-gray-900">Mapbox (Current)</h5>
              </div>
              <div className="space-y-3">
                {mapboxFeatures.map((feature, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <feature.icon className="h-4 w-4 text-blue-600 mt-0.5" />
                    <span className="text-sm text-gray-700">{feature.text}</span>
                  </div>
                ))}
              </div>
              <div className="bg-blue-50 p-3 rounded-lg">
                <p className="text-xs text-blue-800">
                  <strong>Issues:</strong> Getting 403 errors from Zillow proxy, limited property data access
                </p>
              </div>
            </div>

            {/* Google Maps Column */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-600 rounded-full"></div>
                <h5 className="font-semibold text-gray-900">Google Maps (Recommended)</h5>
              </div>
              <div className="space-y-3">
                {googleMapsFeatures.map((feature, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <feature.icon className={`h-4 w-4 mt-0.5 ${
                      feature.status === 'better' ? 'text-green-600' : 'text-gray-600'
                    }`} />
                    <span className="text-sm text-gray-700">{feature.text}</span>
                    {feature.status === 'better' && (
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
                        Better
                      </span>
                    )}
                  </div>
                ))}
              </div>
              <div className="bg-green-50 p-3 rounded-lg">
                <p className="text-xs text-green-800">
                  <strong>Benefits:</strong> Native Google Cloud integration, better geocoding, lower costs, more reliable
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Active Map Display */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h4 className="text-lg font-semibold text-gray-900">
              {activeMap === 'google' ? 'Google Maps' : 'Mapbox'} - Vacant Land Search
            </h4>
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${activeMap === 'google' ? 'bg-green-500' : 'bg-blue-500'}`}></div>
              <span className="text-sm text-gray-600">
                {sampleProperties.length} properties shown
              </span>
            </div>
          </div>
        </div>
        
        <div className="p-6">
          {activeMap === 'google' ? (
            <GoogleMapsComponent
              properties={sampleProperties}
              onPropertyClick={onPropertyClick}
              className="w-full h-96"
            />
          ) : (
            <div className="w-full h-96 flex items-center justify-center bg-gray-100 border border-gray-300 rounded-lg">
              <div className="text-center">
                <Map className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600 mb-2">Mapbox Integration</p>
                <p className="text-sm text-gray-500">
                  Current Mapbox implementation would be displayed here
                </p>
                <p className="text-xs text-red-500 mt-2">
                  Note: Currently experiencing 403 errors from Zillow proxy
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Recommendation */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <div className="bg-green-100 p-2 rounded-full">
            <Zap className="h-5 w-5 text-green-600" />
          </div>
          <div>
            <h4 className="font-semibold text-gray-900 mb-2">Recommendation: Migrate to Google Maps</h4>
            <p className="text-gray-700 text-sm mb-3">
              Based on current Zillow proxy issues and Google Cloud integration benefits, we recommend migrating to Google Maps Platform.
            </p>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Lower costs and better reliability</li>
              <li>• Native Google Cloud integration</li>
              <li>• Superior geocoding and address validation</li>
              <li>• Better property data access through Places API</li>
              <li>• Resolves current Zillow proxy 403 errors</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
