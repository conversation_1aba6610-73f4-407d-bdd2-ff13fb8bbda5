'use client';

import React, { useEffect, useRef, useState } from 'react';
import { Loader } from '@googlemaps/js-api-loader';

interface GoogleMapsComponentProps {
  center?: { lat: number; lng: number };
  zoom?: number;
  properties?: Array<{
    id: string;
    lat: number;
    lng: number;
    address: string;
    price?: number;
    acres?: number;
    zoning?: string;
  }>;
  onPropertyClick?: (property: any) => void;
  className?: string;
}

const GOOGLE_MAPS_API_KEY = 'AIzaSyCWns6m6LcDGlO4ddi3nEXU6BdDicD9SoA';

// Default center: Daytona Beach, FL
const DEFAULT_CENTER = { lat: 29.2108, lng: -81.0228 };

export function GoogleMapsComponent({
  center = DEFAULT_CENTER,
  zoom = 12,
  properties = [],
  onPropertyClick,
  className = 'w-full h-96'
}: GoogleMapsComponentProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<google.maps.Map | null>(null);
  const markersRef = useRef<google.maps.Marker[]>([]);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loader = new Loader({
      apiKey: GOOGLE_MAPS_API_KEY,
      version: 'weekly',
      libraries: ['places', 'geometry']
    });

    loader.load().then(() => {
      setIsLoaded(true);
    }).catch((err) => {
      console.error('Error loading Google Maps:', err);
      setError('Failed to load Google Maps');
    });
  }, []);

  useEffect(() => {
    if (!isLoaded || !mapRef.current) return;

    // Initialize map
    const map = new google.maps.Map(mapRef.current, {
      center,
      zoom,
      mapTypeId: google.maps.MapTypeId.HYBRID, // Show satellite view for land parcels
      styles: [
        {
          featureType: 'poi',
          elementType: 'labels',
          stylers: [{ visibility: 'off' }] // Hide points of interest for cleaner view
        }
      ]
    });

    mapInstanceRef.current = map;

    // Add property markers
    clearMarkers();
    properties.forEach((property) => {
      const marker = new google.maps.Marker({
        position: { lat: property.lat, lng: property.lng },
        map,
        title: property.address,
        icon: {
          url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z" fill="#10B981" stroke="#065F46" stroke-width="2"/>
              <circle cx="12" cy="9" r="2.5" fill="white"/>
            </svg>
          `),
          scaledSize: new google.maps.Size(24, 24),
          anchor: new google.maps.Point(12, 24)
        }
      });

      // Create info window
      const infoWindow = new google.maps.InfoWindow({
        content: `
          <div class="p-3 max-w-xs">
            <h3 class="font-semibold text-gray-900 mb-2">${property.address}</h3>
            ${property.price ? `<p class="text-sm text-gray-600">Price: $${property.price.toLocaleString()}</p>` : ''}
            ${property.acres ? `<p class="text-sm text-gray-600">Size: ${property.acres} acres</p>` : ''}
            ${property.zoning ? `<p class="text-sm text-gray-600">Zoning: ${property.zoning}</p>` : ''}
            <button class="mt-2 bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700" 
                    onclick="window.handlePropertyClick('${property.id}')">
              View Details
            </button>
          </div>
        `
      });

      marker.addListener('click', () => {
        infoWindow.open(map, marker);
      });

      markersRef.current.push(marker);
    });

    // Global function for property click handling
    (window as any).handlePropertyClick = (propertyId: string) => {
      const property = properties.find(p => p.id === propertyId);
      if (property && onPropertyClick) {
        onPropertyClick(property);
      }
    };

  }, [isLoaded, center, zoom, properties, onPropertyClick]);

  const clearMarkers = () => {
    markersRef.current.forEach(marker => marker.setMap(null));
    markersRef.current = [];
  };

  // Geocoding function for address search
  const geocodeAddress = async (address: string): Promise<{ lat: number; lng: number } | null> => {
    if (!isLoaded) return null;

    const geocoder = new google.maps.Geocoder();
    
    try {
      const result = await geocoder.geocode({ address });
      if (result.results[0]) {
        const location = result.results[0].geometry.location;
        return {
          lat: location.lat(),
          lng: location.lng()
        };
      }
    } catch (error) {
      console.error('Geocoding error:', error);
    }
    
    return null;
  };

  // Expose geocoding function
  React.useImperativeHandle(mapRef, () => ({
    geocodeAddress,
    getMap: () => mapInstanceRef.current
  }));

  if (error) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-100 border border-gray-300 rounded-lg`}>
        <div className="text-center">
          <p className="text-red-600 mb-2">Map Error</p>
          <p className="text-sm text-gray-600">{error}</p>
          <p className="text-xs text-gray-500 mt-2">Falling back to Mapbox if available</p>
        </div>
      </div>
    );
  }

  if (!isLoaded) {
    return (
      <div className={`${className} flex items-center justify-center bg-gray-100 border border-gray-300 rounded-lg`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-2"></div>
          <p className="text-sm text-gray-600">Loading Google Maps...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      <div ref={mapRef} className="w-full h-full rounded-lg" />
    </div>
  );
}

// Utility functions for Google Maps services
export const GoogleMapsUtils = {
  // Geocoding
  async geocodeAddress(address: string) {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(address)}&key=${GOOGLE_MAPS_API_KEY}`
    );
    return response.json();
  },

  // Reverse geocoding
  async reverseGeocode(lat: number, lng: number) {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${GOOGLE_MAPS_API_KEY}`
    );
    return response.json();
  },

  // Places search
  async searchPlaces(query: string, location?: { lat: number; lng: number }) {
    const locationParam = location ? `&location=${location.lat},${location.lng}&radius=50000` : '';
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/place/textsearch/json?query=${encodeURIComponent(query)}${locationParam}&key=${GOOGLE_MAPS_API_KEY}`
    );
    return response.json();
  },

  // Address validation
  async validateAddress(address: string) {
    const response = await fetch(
      `https://addressvalidation.googleapis.com/v1:validateAddress?key=${GOOGLE_MAPS_API_KEY}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          address: { addressLines: [address] }
        })
      }
    );
    return response.json();
  }
};
