import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import bcrypt from 'bcryptjs';

// Production database connection for user authentication
// Connects to Google Cloud SQL PostgreSQL database for user management
async function getUserFromDatabase(email: string) {
  try {
    // Make API call to backend to get user data
    const response = await fetch(`https://api.propbolt.com/api/auth/user`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });

    if (!response.ok) {
      return null;
    }

    const userData = await response.json();
    return userData;
  } catch (error) {
    console.error('Error fetching user from database:', error);
    return null;
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        const user = await getUserFromDatabase(credentials.email);

        if (!user) {
          return null;
        }

        // Validate account type - must be either 'land' or 'data'
        if (!user.accountType) {
          console.log(`User ${credentials.email} has NULL account_type, access denied`);
          return null;
        }

        if (user.accountType !== 'land' && user.accountType !== 'data') {
          console.log(`User ${credentials.email} has invalid account_type '${user.accountType}'`);
          return null;
        }

        const isPasswordValid = await bcrypt.compare(credentials.password, user.password);

        if (!isPasswordValid) {
          return null;
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          accountType: user.accountType,
        };
      }
    })
  ],
  session: {
    strategy: 'jwt',
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role;
        token.accountType = user.accountType;
      }
      return token;
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!;
        session.user.role = token.role as string;
        session.user.accountType = token.accountType as string;
      }
      return session;
    },
  },
  pages: {
    signIn: '/land', // Default to land search login
    signOut: '/auth/signout',
  },
  secret: process.env.NEXTAUTH_SECRET || 'your-secret-key-change-in-production',
};
