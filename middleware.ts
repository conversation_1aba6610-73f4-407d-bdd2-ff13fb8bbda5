import { withAuth } from 'next-auth/middleware';
import { NextResponse } from 'next/server';

// Authentication middleware - protects routes based on account type
export default withAuth(
  function middleware(req) {
    const { pathname } = req.nextUrl;

    // Allow access to login pages without authentication
    if (pathname === '/access' || pathname === '/land' || pathname.startsWith('/auth/')) {
      // If user is already authenticated, redirect to appropriate dashboard
      if (req.nextauth.token) {
        if (req.nextauth.token.accountType === 'data') {
          return NextResponse.redirect(new URL('/api-dashboard', req.url));
        } else if (req.nextauth.token.accountType === 'land') {
          return NextResponse.redirect(new URL('/', req.url));
        }
      }
      return NextResponse.next();
    }

    // Redirect to appropriate login if not authenticated
    if (!req.nextauth.token) {
      // API dashboard routes require API access login
      if (pathname.startsWith('/api-dashboard') || pathname.startsWith('/api-docs')) {
        return NextResponse.redirect(new URL('/access', req.url));
      }
      // All other routes require land search login
      const signInUrl = new URL('/land', req.url);
      signInUrl.searchParams.set('callbackUrl', req.url);
      return NextResponse.redirect(signInUrl);
    }

    // Route protection based on account type
    const accountType = req.nextauth.token.accountType;

    // API dashboard routes - require 'data' account type
    if (pathname.startsWith('/api-dashboard') || pathname.startsWith('/api-docs')) {
      if (accountType !== 'data') {
        return NextResponse.redirect(new URL('/access?error=access_denied', req.url));
      }
    }

    // Land search routes - require 'land' account type
    else if (pathname === '/' || pathname.startsWith('/search') || pathname.startsWith('/analytics') ||
             pathname.startsWith('/reports') || pathname.startsWith('/settings') || pathname.startsWith('/watchlist')) {
      if (accountType !== 'land') {
        return NextResponse.redirect(new URL('/land?error=access_denied', req.url));
      }
    }

    return NextResponse.next();
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl;

        // Allow access to login pages
        if (pathname === '/access' || pathname === '/land' || pathname.startsWith('/auth/')) {
          return true;
        }

        // Require authentication for all other pages
        return !!token;
      },
    },
  }
);

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (all API routes including NextAuth)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api|_next/static|_next/image|favicon.ico|public).*)',
  ],
};
